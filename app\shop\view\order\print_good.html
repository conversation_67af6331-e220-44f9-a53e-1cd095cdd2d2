<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        window.ns_url = {
            baseUrl: "ROOT_URL/",
            route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
        };
    </script>
    <script type="text/javascript" src="__STATIC__/js/common.js" charset="utf-8"></script>
    <script type="text/javascript" src="__STATIC__/js/jquery.printarea.js" charset="utf-8"></script>
    <title>{$menu_info['title']|default="打印封口单"} - {$shop_info['site_name']|default=""}</title>
</head>
<body>
{notempty name="order_detail"}
<div class="print-layout">
    <!-- 屏幕端工具栏（不会参与打印） -->
    <div class="print-toolbar" style="position: fixed; top: 10px; right: 20px; z-index: 9999; display: flex; gap: 8px;">
        <button id="printbtn" type="button" style="padding: 6px 12px;">打印</button>
        <button id="toggle-preview" type="button" style="padding: 6px 12px;">关闭分页预览</button>
    </div>
    <div class="print-page">
        <div id="printarea">
            <style type="text/css">
                html,
                body {
                    box-sizing: border-box;
                    width: 100%;
                    height: 100%;
                    margin: 0;
                    padding: 0;
                }

                .shrink2 {
                    text-indent: 2em;
                    font-size: 14px;
                    color: #000;
                }

                #maipage {
                    width: calc(100% - 20%);
                    padding: 90px 10% 20px 10%;
                    font-size: 20px;
                    font-weight: 600;
                    color: #000;
                    height:260mm;
                    border: 1px solid #999;
                }

                .noshrink {
                    line-height: 1.6em;
                }

                .shrink3 {
                    text-indent: 3em;
                    line-height: 1.6em;
                }

                .sectionbottom {
                    margin-bottom: 1.8em;
                }

                .textshrink {
                    margin-left: 20px;
                }

                .pagefot {
                    text-align: center;
                    font-family: "Arial", "Microsoft YaHei", "黑体", "宋体", sans-serif;
                    font-size: 12px;
                }

                body {
                    background-color: white;
                }

                .w {
                    width: 1200px;
                    margin: 0 auto;
                    min-height: 1800px;
                }

                h3 {
                    padding-left: 5px;
                }

                .box1 {
                    /* width: 600px; */
                    width: 500px;
                    margin-left: -27px;
                    margin-top: 70px;
                }

                .box1>div {
                    width: 450px;
                    margin: 0 auto;
                }

                .box1 h1 {
                    text-align: center;
                    color: #1E1F1E;
                    font-size: 55px;
                }

                .box1 .tips {
                    color: #444443;
                    padding-top: 5px;
                    padding-bottom: 25px;
                    letter-spacing: 2.5px;
                }

                .box1 .arrange {
                    padding: 15px 10px;
                    border-radius: 15px;
                    margin-bottom: 60px;
                }

                .box1 .arrange .title {
                    color: #000;
                    font-weight: 600;
                    font-size: 28px;
                }

                .box1 .arrange .tag {
                    color: #000;
                    display: inline-block;
                    font-weight: 600;
                    height: 28px;
                    width: 270px;
                    margin-bottom: 10px;
                    font-size: 24px;
                }

                .box1 .details {
                    color: #000;
                    font-size: 30px;
                    font-weight: 600;
                    padding-bottom: 7px;
                }

                .directions {
                    /* padding-bottom: 58px; */
                }

                .directions h1 {
                    text-align: left;
                }

                .directions .step {
                    overflow: hidden;
                    padding: 20px 0 25px 0;
                }

                .directions .step>div {
                    float: left;
                    width: 30.333333%;
                    padding: 7px;
                    text-align: center;
                }

                .directions .step img {
                    width: 100%;
                    padding-bottom: 15px;
                }

                .directions .step i {
                    font-style: normal;
                    font-size: 25px;
                    font-weight: 600;
                    color: #000;
                    padding-bottom: 15px;
                    display: inline-block;
                }

                .directions .step span {
                    display: inline-block;
                    width: 80%;
                    text-align: left;
                    color: #000;
                    font-size: 14px;
                }

                .directions .key {
                    color: #000;
                    font-size: 14px;
                    padding-bottom: 107px;
                }

                .notes .item {
                    background-color: #DBDADA;
                    border-radius: 15px;
                    padding: 15px 5px;
                }

                .notes .item ul {
                    padding: 0;
                    margin: 0;
                    list-style: none;
                }

                .notes .item ul li {
                    display: flex;
                    padding: 0 0 15px 0;
                    font-size: 14px;
                }

                .notes .item img {
                    width: 25px;
                    padding: 5px 15px 5px 5px;
                }


                /* 右边的盒子 */

                .box2 {
                    width: 600px;
                    /* width: 578px; */
                    padding-left: 17px;
                }

                .morning {
                    margin-top: 30px;
                    border: 1px solid #535354;
                    border-radius: 15px 15px 0 0;
                    padding: 15px;
                    overflow: hidden;
                }

                .breakfast,
                .dinner {
                    border-left: 1px solid #535354;
                    border-right: 1px solid #535354;
                    border-bottom: 1px solid #535354;
                    padding: 15px;
                    overflow: hidden;
                }

                .bedtime {
                    border-left: 1px solid #535354;
                    border-right: 1px solid #535354;
                    border-bottom: 1px solid #535354;
                    border-radius: 0 0 15px 15px;
                    padding: 15px;
                    overflow: hidden;
                }

                .morning .text {
                    background-color: #CAA723;
                }

                .breakfast .text {
                    background-color: #C291A8;
                }

                .dinner .text {
                    background-color: #BD9E2B;
                }

                .bedtime .text {
                    background-color: #C190A7;
                }

                .box2>div>div {
                    float: left;
                }

                .title-lelt {
                    width: 96px;
                }

                .title-lelt span {
                    font-size: 23px;
                    letter-spacing: 5px;
                }

                .title-lelt i {
                    font-size: 10px;
                    font-style: normal;
                }

                .title-lelt img {
                    width: 100%;
                    padding-top: 15px;
                }

                .title-right {
                    width: 450px;
                    padding-left: 20px;
                }

                .title-right>span {
                    display: inline-block;
                    padding-bottom: 5px;
                    padding-left: 7px;
                }

                .title-right .list {
                    padding: 0;
                    margin: 0;
                    list-style: none;
                    display: flex;
                    flex-wrap: wrap;
                }

                .list>li {
                    width: 30.333333%;
                    padding: 5px;
                }

                .list .text {
                    color: white;
                    height: 85px;
                    border-radius: 5px 5px 0px 0px;
                    text-align: center;
                }

                .list .text span {
                    font-size: 14px;
                }

                .list .text span:nth-child(1) {
                    display: inline-block;
                    padding-top: 15px;
                }

                .list .img {
                    background-color: white;
                    color: #999797;
                    height: 65px;
                    border: 1px solid #999797;
                    border-top: 0px;
                    border-radius: 0 0 5px 5px;
                    text-align: center;
                    font-weight: 600;
                }

                .list .img img {
                    width: 50px;
                    padding-top: 10px;
                    height: 25px;
                }
                
                /* 药品安排样式 */
                .medicine-arrange {
                    margin-top: 30px;
                    padding: 20px;
                    border: 3px solid #999;
                    border-radius: 10px;
                }
                
                .medicine-arrange .arrange-title {
                    color: #000;
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 15px;
                }
                
                .time-period {
                    margin-bottom: 20px;
                }
                
                .time-period-title {
                    color: #000;
                    font-size: 20px;
                    font-weight: 600;
                    margin-bottom: 8px;
                }
                
                .medicine-item {
                    color: #000;
                    font-size: 16px;
                    margin-left: 20px;
                    margin-bottom: 3px;
                }
                
                .service-time {
                    color: #000;
                    font-size: 16px;
                    margin-left: 0px;
                    margin-bottom: 5px;
                }
                
                .production-date {
                    color: #000;
                    font-size: 16px;
                    margin-left: 0px;
                    margin-bottom: 10px;
                }
            </style>
            <div class="w">
                <div class="content">
                    <div class="box1" style="">
                        <div class="arrange">
                            <!-- 药品安排分页：每个非空餐包单独一页 -->
                            {notempty name="arrange_data"}
                            <style type="text/css">
                                .page {
                                    page-break-after: always;
                                    break-after: page;
                                }
                                .page:last-child {
                                    page-break-after: auto;
                                    break-after: auto;
                                }
                                /* 屏幕端显示为独立纸张，便于预览分页（通过 .screen-paged 开关） */
                                @media screen {
                                    .screen-paged .page {
                                        width: 210mm;              /* A4 宽度 */
                                        min-height: 297mm;         /* 满 A4 高度，预览更直观 */
                                        margin: 10mm auto;         /* 居中并上下留白 */
                                        padding: 8mm;              /* 页面内边距 */
                                        border: 1px solid #e5e5e5; /* 纸张边框 */
                                        background: #fff;
                                        box-shadow: 0 2mm 6mm rgba(0,0,0,0.08);
                                    }
                                    .screen-paged .page + .page { margin-top: 12mm; }
                                }
                                @media print {
                                    .page {
                                        page-break-after: always;
                                        width: auto;
                                        min-height: auto;
                                        margin: 0;
                                        padding: 0;
                                        border: none;
                                        box-shadow: none;
                                    }
                                    .print-toolbar { display: none !important; }
                                }
                                .page-header {
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    font-size: 30px;
                                    font-weight: 600;
                                    margin-bottom: 12px;
                                    position: relative;
                                    padding-bottom: 8px;
                                    color: #000;
                                }
                                .page-header::after {
                                    content: '';
                                    position: absolute;
                                    left: 0;
                                    bottom: 0;
                                    width: 50%;
                                    border-bottom: 2px solid #000;
                                }
                            </style>

                            {php}
                            $__ordered = [];
                            $__slots__ = ['earlymoning','moning','aftnoon','canjian','night','sleep','evening'];
                            foreach ($__slots__ as $__s) {
                                if (isset($arrange_data[$__s]) && !empty($arrange_data[$__s]['goods'])) {
                                    $__ordered[] = $arrange_data[$__s];
                                }
                            }
                            {/php}

                            {volist name="__ordered" id="time_data"}
                            <div class="page">
                                <div class="medicine-arrange">
                                    <div class="page-header">
                                        <span>{$order_detail.name}</span>
                                    </div>
                                    <div class="time-period">
                                        <div class="time-period-title">{$time_data.name}*{$attr_class_info.day}包：</div>
                                        {volist name="time_data.goods" id="goods"}
                                        <div class="medicine-item">{$goods.num}*{$goods.sku_name}</div>
                                        {/volist}
                                        <div class="service-time">服用时间：{$time_data.service_time}</div>
                                        <div class="production-date">生产日期：{$production_date}</div>
                                    </div>
                                </div>
                            </div>
                            {/volist}
                            {/notempty}

                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
{/notempty}
</body>
<script>
    $(function(){
        // 默认开启屏幕端分页预览
        $('body').addClass('screen-paged');

        // 打印按钮
        $("#printbtn").click(function(){
            $("#printarea").printArea();
        });

        // 分页预览开关
        $('#toggle-preview').on('click', function(){
            $('body').toggleClass('screen-paged');
            if ($('body').hasClass('screen-paged')) {
                $(this).text('关闭分页预览');
            } else {
                $(this).text('开启分页预览');
            }
        });
    });
</script>
</html>